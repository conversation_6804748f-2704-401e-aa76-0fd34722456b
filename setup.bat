@echo off
echo ========================================
echo Face Recognition Attendance System Setup
echo MongoDB Version
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.7+ from https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
    exit /b 1
)

echo Python found! Installing required packages...
echo.

echo Installing Flask and web framework dependencies...
pip install Flask==2.3.3 Flask-CORS==4.0.0 Werkzeug==2.3.7

echo Installing MongoDB dependencies...
pip install pymongo==4.6.0 Flask-PyMongo==2.3.0 bson==0.5.10

echo Installing computer vision dependencies...
pip install opencv-python==******** numpy==1.24.3 Pillow==10.0.1

echo Installing face recognition dependencies...
pip install cmake
pip install dlib
pip install face-recognition==1.3.0

echo Installing additional utilities...
pip install python-dotenv==1.0.0

echo.
echo ========================================
echo Python packages installed successfully!
echo ========================================
echo.

echo IMPORTANT: MongoDB Setup Required
echo.
echo 1. Install MongoDB Community Server from:
echo    https://www.mongodb.com/try/download/community
echo.
echo 2. Start MongoDB service:
echo    - Windows: MongoDB should start automatically after installation
echo    - Or manually: net start MongoDB
echo.
echo 3. Setup database (optional):
echo    python setup_mongodb.py
echo.
echo 4. Run the application:
echo    python app.py
echo.
echo 5. Open browser to: http://localhost:5000
echo.
pause
