{% extends "base.html" %}

{% block title %}Mark Attendance - Face Recognition Attendance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Mark Attendance
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Select Action</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="action" id="checkin" value="in" checked>
                                <label class="btn btn-outline-success" for="checkin">
                                    <i class="fas fa-sign-in-alt me-2"></i>Check In
                                </label>
                                
                                <input type="radio" class="btn-check" name="action" id="checkout" value="out">
                                <label class="btn btn-outline-danger" for="checkout">
                                    <i class="fas fa-sign-out-alt me-2"></i>Check Out
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-primary w-100" id="startCamera">
                                <i class="fas fa-camera me-2"></i>Start Camera
                            </button>
                        </div>
                        
                        <div class="mb-3">
                            <button type="button" class="btn btn-success w-100" id="markAttendance" disabled>
                                <i class="fas fa-check me-2"></i>Mark Attendance
                            </button>
                        </div>

                        <div id="currentTime" class="text-center">
                            <h5 class="text-muted">Current Time</h5>
                            <h3 id="timeDisplay" class="text-primary"></h3>
                            <p id="dateDisplay" class="text-muted"></p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="text-center">
                            <video id="video" width="300" height="225" autoplay style="display: none;"></video>
                            <canvas id="canvas" width="300" height="225" style="display: none;"></canvas>
                            <div id="cameraPreview" class="border rounded p-3" style="min-height: 225px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                <div class="text-muted">
                                    <i class="fas fa-video fa-3x mb-2"></i>
                                    <p>Click "Start Camera" to begin</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessages" class="mt-4"></div>

        <!-- Recent Attendance -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Today's Attendance
                </h5>
            </div>
            <div class="card-body">
                <div id="todayAttendance">
                    <div class="text-center text-muted">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p>No attendance records for today yet.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Attendance Marked Successfully
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <div id="successContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div class="modal fade" id="processingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Processing Face Recognition...</h5>
                <p class="text-muted">Please wait while we identify you.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/attendance.js') }}"></script>
{% endblock %}
