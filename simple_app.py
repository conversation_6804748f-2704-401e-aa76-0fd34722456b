#!/usr/bin/env python3
"""
Simple Face Recognition Attendance System - Working Version
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime, date
from werkzeug.utils import secure_filename
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

CORS(app)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# In-memory storage for demo
users_db = []
attendance_db = []

@app.route('/')
def index():
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Face Recognition Attendance System</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-user-check me-2"></i>
                    Face Attendance System
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">Home</a>
                    <a class="nav-link" href="/register">Register</a>
                    <a class="nav-link" href="/attendance">Attendance</a>
                    <a class="nav-link" href="/records">Records</a>
                </div>
            </div>
        </nav>

        <main class="container mt-4">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="text-center mb-5">
                        <h1 class="display-4 text-primary">
                            <i class="fas fa-user-check me-3"></i>
                            Face Recognition Attendance System
                        </h1>
                        <p class="lead text-muted">
                            Modern, secure, and efficient attendance tracking using facial recognition technology
                        </p>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Demo Mode:</strong> This version works without face recognition for testing purposes.
                        </div>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-user-plus fa-3x text-success"></i>
                                    </div>
                                    <h5 class="card-title">Register New User</h5>
                                    <p class="card-text">
                                        Register a new user by capturing their face and personal information.
                                    </p>
                                    <a href="/register" class="btn btn-success">
                                        <i class="fas fa-plus me-2"></i>Register Now
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-clock fa-3x text-primary"></i>
                                    </div>
                                    <h5 class="card-title">Mark Attendance</h5>
                                    <p class="card-text">
                                        Quick and easy attendance marking using face recognition.
                                    </p>
                                    <a href="/attendance" class="btn btn-primary">
                                        <i class="fas fa-camera me-2"></i>Mark Attendance
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-chart-bar fa-3x text-info"></i>
                                    </div>
                                    <h5 class="card-title">View Records</h5>
                                    <p class="card-text">
                                        View comprehensive attendance records and generate reports.
                                    </p>
                                    <a href="/records" class="btn btn-info">
                                        <i class="fas fa-list me-2"></i>View Records
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100 shadow-sm">
                                <div class="card-body text-center">
                                    <div class="mb-3">
                                        <i class="fas fa-cog fa-3x text-warning"></i>
                                    </div>
                                    <h5 class="card-title">System Status</h5>
                                    <p class="card-text">
                                        <span class="badge bg-success">Demo Mode Active</span><br>
                                        Users: <span id="userCount">{}</span><br>
                                        Records: <span id="recordCount">{}</span>
                                    </p>
                                    <a href="/demo" class="btn btn-warning">
                                        <i class="fas fa-info me-2"></i>Demo Info
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    </body>
    </html>
    """.format(len(users_db), len(attendance_db))

@app.route('/register')
def register_page():
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Register User - Face Recognition Attendance</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">
                    <i class="fas fa-user-check me-2"></i>
                    Face Attendance System
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">Home</a>
                    <a class="nav-link active" href="/register">Register</a>
                    <a class="nav-link" href="/attendance">Attendance</a>
                    <a class="nav-link" href="/records">Records</a>
                </div>
            </div>
        </nav>

        <main class="container mt-4">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                Register New User
                            </h4>
                        </div>
                        <div class="card-body">
                            <form id="registerForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Full Name</label>
                                            <input type="text" class="form-control" id="name" name="name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                        <div class="mb-3">
                                            <button type="button" class="btn btn-primary" id="captureBtn">
                                                <i class="fas fa-camera me-2"></i>Capture Face
                                            </button>
                                            <button type="submit" class="btn btn-success ms-2" id="submitBtn">
                                                <i class="fas fa-save me-2"></i>Register User
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="text-center">
                                            <video id="video" width="300" height="225" autoplay style="display: none;"></video>
                                            <canvas id="canvas" width="300" height="225" style="display: none;"></canvas>
                                            <div id="preview" class="border rounded p-3" style="min-height: 225px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                                <div class="text-muted">
                                                    <i class="fas fa-camera fa-3x mb-2"></i>
                                                    <p>Click "Capture Face" to take a photo</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div id="message" class="mt-3"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            let video = document.getElementById('video');
            let canvas = document.getElementById('canvas');
            let preview = document.getElementById('preview');
            let captureBtn = document.getElementById('captureBtn');
            let submitBtn = document.getElementById('submitBtn');
            let capturedImage = null;
            let cameraStarted = false;

            captureBtn.addEventListener('click', async function() {
                if (!cameraStarted) {
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                        video.srcObject = stream;
                        video.style.display = 'block';
                        preview.innerHTML = '';
                        preview.appendChild(video);
                        cameraStarted = true;
                        captureBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Capture Photo';
                    } catch (error) {
                        showMessage('Error accessing camera: ' + error.message, 'danger');
                    }
                } else {
                    const context = canvas.getContext('2d');
                    context.drawImage(video, 0, 0, canvas.width, canvas.height);
                    capturedImage = canvas.toDataURL('image/jpeg', 0.8);
                    
                    const img = document.createElement('img');
                    img.src = capturedImage;
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'cover';
                    
                    preview.innerHTML = '';
                    preview.appendChild(img);
                    
                    video.srcObject.getTracks().forEach(track => track.stop());
                    video.style.display = 'none';
                    cameraStarted = false;
                    captureBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Capture Face';
                }
            });

            document.getElementById('registerForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                
                if (!name || !email) {
                    showMessage('Please fill in all fields', 'warning');
                    return;
                }
                
                if (!capturedImage) {
                    showMessage('Please capture a photo first', 'warning');
                    return;
                }
                
                try {
                    const response = await fetch('/api/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ name, email, image: capturedImage })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        showMessage(result.message, 'success');
                        document.getElementById('registerForm').reset();
                        preview.innerHTML = '<div class="text-muted"><i class="fas fa-camera fa-3x mb-2"></i><p>Click "Capture Face" to take a photo</p></div>';
                        capturedImage = null;
                    } else {
                        showMessage(result.error, 'danger');
                    }
                } catch (error) {
                    showMessage('Registration failed: ' + error.message, 'danger');
                }
            });

            function showMessage(message, type) {
                document.getElementById('message').innerHTML = 
                    '<div class="alert alert-' + type + '">' + message + '</div>';
            }
        </script>
    </body>
    </html>
    """

@app.route('/attendance')
def attendance_page():
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Mark Attendance - Face Recognition</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">Face Attendance System</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">Home</a>
                    <a class="nav-link" href="/register">Register</a>
                    <a class="nav-link active" href="/attendance">Attendance</a>
                    <a class="nav-link" href="/records">Records</a>
                </div>
            </div>
        </nav>

        <main class="container mt-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Mark Attendance</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Action</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="action" id="checkin" value="in" checked>
                                    <label class="btn btn-outline-success" for="checkin">Check In</label>
                                    <input type="radio" class="btn-check" name="action" id="checkout" value="out">
                                    <label class="btn btn-outline-danger" for="checkout">Check Out</label>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary w-100 mb-3" id="markBtn">
                                <i class="fas fa-camera me-2"></i>Mark Attendance (Demo)
                            </button>
                            <div id="message"></div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <div class="border rounded p-3" style="min-height: 200px; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-muted">
                                        <i class="fas fa-video fa-3x mb-2"></i>
                                        <p>Demo Mode - No camera required</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <script>
            document.getElementById('markBtn').addEventListener('click', async function() {
                const action = document.querySelector('input[name="action"]:checked').value;
                
                try {
                    const response = await fetch('/api/mark_attendance', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ image: 'demo_image', action })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok) {
                        showMessage(result.message, 'success');
                    } else {
                        showMessage(result.error, 'danger');
                    }
                } catch (error) {
                    showMessage('Failed to mark attendance: ' + error.message, 'danger');
                }
            });

            function showMessage(message, type) {
                document.getElementById('message').innerHTML = 
                    '<div class="alert alert-' + type + '">' + message + '</div>';
            }
        </script>
    </body>
    </html>
    """

@app.route('/records')
def records_page():
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Attendance Records</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="/">Face Attendance System</a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="/">Home</a>
                    <a class="nav-link" href="/register">Register</a>
                    <a class="nav-link" href="/attendance">Attendance</a>
                    <a class="nav-link active" href="/records">Records</a>
                </div>
            </div>
        </nav>

        <main class="container mt-4">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">Attendance Records</h4>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary mb-3" onclick="loadRecords()">
                        <i class="fas fa-sync me-2"></i>Refresh Records
                    </button>
                    <div id="recordsTable"></div>
                </div>
            </div>
        </main>

        <script>
            async function loadRecords() {
                try {
                    const response = await fetch('/api/attendance_records');
                    const records = await response.json();
                    
                    let html = '<div class="table-responsive"><table class="table table-striped">';
                    html += '<thead><tr><th>Name</th><th>Email</th><th>Date</th><th>Check In</th><th>Check Out</th><th>Status</th></tr></thead><tbody>';
                    
                    if (records.length === 0) {
                        html += '<tr><td colspan="6" class="text-center">No records found</td></tr>';
                    } else {
                        records.forEach(record => {
                            html += '<tr>';
                            html += '<td>' + record.name + '</td>';
                            html += '<td>' + record.email + '</td>';
                            html += '<td>' + record.date + '</td>';
                            html += '<td>' + (record.time_in || '-') + '</td>';
                            html += '<td>' + (record.time_out || '-') + '</td>';
                            html += '<td><span class="badge bg-success">' + record.status + '</span></td>';
                            html += '</tr>';
                        });
                    }
                    
                    html += '</tbody></table></div>';
                    document.getElementById('recordsTable').innerHTML = html;
                } catch (error) {
                    document.getElementById('recordsTable').innerHTML = 
                        '<div class="alert alert-danger">Failed to load records: ' + error.message + '</div>';
                }
            }
            
            // Load records on page load
            loadRecords();
        </script>
    </body>
    </html>
    """

# API Routes
@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        image_data = data.get('image')
        
        if not all([name, email, image_data]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Check if user already exists
        for user in users_db:
            if user['email'] == email:
                return jsonify({'error': 'User already registered'}), 400
        
        # Create new user
        user_doc = {
            'id': len(users_db) + 1,
            'name': name,
            'email': email,
            'created_at': datetime.utcnow().isoformat()
        }
        
        users_db.append(user_doc)
        
        return jsonify({
            'message': f'User {name} registered successfully! (Demo Mode)',
            'user_id': user_doc['id']
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/mark_attendance', methods=['POST'])
def mark_attendance():
    try:
        data = request.get_json()
        action = data.get('action', 'in')
        
        if not users_db:
            return jsonify({'error': 'No users registered yet. Please register first.'}), 404
        
        # Use the most recently registered user for demo
        recognized_user = users_db[-1]
        
        # Mark attendance
        today = date.today().isoformat()
        attendance = None
        
        for record in attendance_db:
            if record['user_id'] == recognized_user['id'] and record['date'] == today:
                attendance = record
                break
        
        current_time = datetime.now()
        
        if not attendance:
            attendance = {
                'id': len(attendance_db) + 1,
                'user_id': recognized_user['id'],
                'user_name': recognized_user['name'],
                'user_email': recognized_user['email'],
                'date': today,
                'time_in': None,
                'time_out': None,
                'status': 'present'
            }
            attendance_db.append(attendance)
        
        if action == 'in':
            if attendance.get('time_in'):
                return jsonify({'error': 'Already marked in for today'}), 400
            
            attendance['time_in'] = current_time.isoformat()
            message = f'Welcome {recognized_user["name"]}! Attendance marked for check-in. (Demo Mode)'
            
        else:  # action == 'out'
            if not attendance.get('time_in'):
                return jsonify({'error': 'Must check in first'}), 400
            if attendance.get('time_out'):
                return jsonify({'error': 'Already marked out for today'}), 400
            
            attendance['time_out'] = current_time.isoformat()
            message = f'Goodbye {recognized_user["name"]}! Attendance marked for check-out. (Demo Mode)'
        
        return jsonify({
            'message': message,
            'user': recognized_user['name'],
            'time': current_time.strftime('%H:%M:%S')
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/attendance_records')
def get_attendance_records():
    try:
        result = []
        for record in attendance_db:
            time_in = None
            time_out = None
            
            if record.get('time_in'):
                time_in = datetime.fromisoformat(record['time_in']).strftime('%H:%M:%S')
            if record.get('time_out'):
                time_out = datetime.fromisoformat(record['time_out']).strftime('%H:%M:%S')
            
            result.append({
                'id': record['id'],
                'name': record.get('user_name', 'Unknown'),
                'email': record.get('user_email', 'Unknown'),
                'date': record['date'],
                'time_in': time_in,
                'time_out': time_out,
                'status': record.get('status', 'present')
            })
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/demo')
def demo_info():
    return jsonify({
        'mode': 'Demo Mode',
        'message': 'This is a simplified demo version',
        'users_count': len(users_db),
        'attendance_count': len(attendance_db),
        'users': [{'name': u['name'], 'email': u['email']} for u in users_db],
        'features': [
            'User registration (simplified)',
            'Attendance marking (simulated)',
            'Records viewing',
            'In-memory storage'
        ]
    })

if __name__ == '__main__':
    print("=" * 60)
    print("Face Recognition Attendance System - SIMPLE DEMO")
    print("=" * 60)
    print("🚀 Starting server...")
    print("📱 Open your browser to: http://localhost:5000")
    print("=" * 60)
    
    app.run(debug=True, host='127.0.0.1', port=5000)
