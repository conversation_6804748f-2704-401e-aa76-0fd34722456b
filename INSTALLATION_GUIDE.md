# Installation Guide - Face Recognition Attendance System

## Prerequisites

### 1. Install Python
- Download Python 3.7+ from [python.org](https://python.org/downloads/)
- **IMPORTANT**: During installation, check "Add Python to PATH"
- Verify installation by opening Command Prompt and typing: `python --version`

### 2. Install Visual Studio Build Tools (Windows)
- Download from [Microsoft Visual Studio](https://visualstudio.microsoft.com/visual-cpp-build-tools/)
- Install "C++ build tools" workload
- This is required for compiling dlib and face_recognition libraries

### 3. System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: At least 2GB free space
- **Camera**: Webcam or built-in camera
- **Browser**: Chrome, Firefox, Safari, or Edge (latest versions)

## Installation Methods

### Method 1: Automated Setup (Recommended)

1. **Extract the project** to your desired location
2. **Run setup script**:
   - Double-click `setup.bat` for system-wide installation
   - OR double-click `setup_venv.bat` for virtual environment (recommended)

3. **Start the application**:
   - Double-click `run.bat`
   - OR activate virtual environment and run `python app.py`

### Method 2: Manual Installation

1. **Open Command Prompt** in the project directory
2. **Create virtual environment** (optional but recommended):
   ```cmd
   python -m venv face_attendance_env
   face_attendance_env\Scripts\activate
   ```

3. **Install dependencies**:
   ```cmd
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```cmd
   python app.py
   ```

### Method 3: Step-by-Step Package Installation

If you encounter issues with requirements.txt, install packages individually:

```cmd
# Core web framework
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.1.1
pip install Flask-CORS==4.0.0
pip install Werkzeug==2.3.7

# Computer vision
pip install opencv-python==********
pip install numpy==1.24.3
pip install Pillow==10.0.1

# Face recognition (install in order)
pip install cmake
pip install dlib
pip install face-recognition==1.3.0

# Utilities
pip install python-dotenv==1.0.0
pip install SQLAlchemy==2.0.23
```

## Troubleshooting

### Common Installation Issues

#### 1. "Python not found" Error
**Solution**: 
- Reinstall Python with "Add to PATH" option checked
- OR manually add Python to system PATH

#### 2. dlib Installation Fails
**Solutions**:
- Install Visual Studio Build Tools
- Use pre-compiled wheel: `pip install dlib-binary`
- On Windows: `pip install cmake` first

#### 3. face_recognition Installation Fails
**Solutions**:
- Ensure dlib is installed first
- Install cmake: `pip install cmake`
- Use conda instead: `conda install -c conda-forge dlib face_recognition`

#### 4. OpenCV Issues
**Solutions**:
- Try: `pip install opencv-python-headless`
- Ensure no conflicting OpenCV installations

#### 5. Permission Errors
**Solutions**:
- Run Command Prompt as Administrator
- Use virtual environment
- Add `--user` flag: `pip install --user package_name`

### Platform-Specific Notes

#### Windows
- Install Visual Studio Build Tools
- Use Command Prompt or PowerShell
- Antivirus may interfere with installation

#### macOS
- Install Xcode Command Line Tools: `xcode-select --install`
- Use Terminal application
- May need to install Homebrew

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3-pip python3-venv
sudo apt install cmake build-essential
sudo apt install libopencv-dev python3-opencv
```

## Verification

### Test Installation
1. **Check Python**: `python --version`
2. **Check packages**: `pip list`
3. **Test imports**:
   ```python
   python -c "import cv2, face_recognition, flask; print('All packages imported successfully!')"
   ```

### Run Application
1. **Start server**: `python app.py`
2. **Check output**: Should see "Running on http://127.0.0.1:5000"
3. **Open browser**: Navigate to `http://localhost:5000`
4. **Test camera**: Go to Register page and test camera access

## Performance Optimization

### For Better Performance
1. **Use SSD storage** for faster database operations
2. **Close unnecessary applications** to free RAM
3. **Use good lighting** for better face recognition
4. **Update camera drivers** for optimal video quality

### Memory Usage
- **Minimum**: 2GB RAM available
- **Recommended**: 4GB+ RAM available
- **Face recognition**: Uses ~100-200MB per user

## Security Considerations

### Network Security
- Application runs on localhost by default
- For network access, modify `app.run()` in app.py
- Use HTTPS in production environments

### Data Security
- Face encodings are stored securely in database
- User photos stored in `static/uploads/` directory
- Regular database backups recommended

## Getting Help

### If Installation Fails
1. **Check Python version**: Must be 3.7+
2. **Check internet connection**: Required for package downloads
3. **Check disk space**: Need at least 2GB free
4. **Check antivirus**: May block installations
5. **Try virtual environment**: Isolates dependencies

### Support Resources
- **Python Installation**: [python.org/downloads](https://python.org/downloads/)
- **Visual Studio Build Tools**: [Microsoft Docs](https://docs.microsoft.com/en-us/cpp/build/vscpp-step-0-installation)
- **OpenCV Documentation**: [opencv.org](https://opencv.org/)
- **Face Recognition Library**: [GitHub Repository](https://github.com/ageitgey/face_recognition)

## Next Steps

After successful installation:
1. **Read README.md** for usage instructions
2. **Test face registration** with good lighting
3. **Configure camera settings** if needed
4. **Set up regular backups** of attendance.db
5. **Customize appearance** by modifying CSS files

## Production Deployment

For production use:
1. **Use proper WSGI server** (Gunicorn, uWSGI)
2. **Set up reverse proxy** (Nginx, Apache)
3. **Use PostgreSQL/MySQL** instead of SQLite
4. **Implement proper authentication**
5. **Set up SSL/HTTPS**
6. **Configure firewall rules**
