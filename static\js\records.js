// Records page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const recordsTableBody = document.getElementById('recordsTableBody');
    const refreshBtn = document.getElementById('refreshBtn');
    const exportBtn = document.getElementById('exportBtn');
    const dateFilter = document.getElementById('dateFilter');
    const userFilter = document.getElementById('userFilter');
    const statusFilter = document.getElementById('statusFilter');
    const applyFiltersBtn = document.getElementById('applyFilters');
    
    // Statistics elements
    const totalPresent = document.getElementById('totalPresent');
    const totalAbsent = document.getElementById('totalAbsent');
    const totalLate = document.getElementById('totalLate');
    const totalUsers = document.getElementById('totalUsers');
    
    let allRecords = [];
    let filteredRecords = [];
    let currentPage = 1;
    const recordsPerPage = 10;

    // Initialize
    loadRecords();
    loadUsers();
    
    // Set today's date as default
    dateFilter.value = new Date().toISOString().split('T')[0];

    // Event listeners
    refreshBtn.addEventListener('click', loadRecords);
    exportBtn.addEventListener('click', exportRecords);
    applyFiltersBtn.addEventListener('click', applyFilters);

    async function loadRecords() {
        try {
            showLoading();
            const records = await FaceAttendance.makeAPIRequest('/api/attendance_records');
            allRecords = records;
            applyFilters();
            updateStatistics();
        } catch (error) {
            console.error('Failed to load records:', error);
            showError('Failed to load attendance records.');
        }
    }

    async function loadUsers() {
        try {
            const users = await FaceAttendance.makeAPIRequest('/api/users');
            populateUserFilter(users);
        } catch (error) {
            console.error('Failed to load users:', error);
        }
    }

    function populateUserFilter(users) {
        userFilter.innerHTML = '<option value="">All Users</option>';
        users.forEach(user => {
            const option = document.createElement('option');
            option.value = user.email;
            option.textContent = user.name;
            userFilter.appendChild(option);
        });
    }

    function applyFilters() {
        filteredRecords = allRecords.filter(record => {
            let match = true;
            
            // Date filter
            if (dateFilter.value) {
                match = match && record.date === dateFilter.value;
            }
            
            // User filter
            if (userFilter.value) {
                match = match && record.email === userFilter.value;
            }
            
            // Status filter
            if (statusFilter.value) {
                match = match && record.status === statusFilter.value;
            }
            
            return match;
        });
        
        currentPage = 1;
        displayRecords();
        updatePagination();
    }

    function displayRecords() {
        if (filteredRecords.length === 0) {
            recordsTableBody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="py-4">
                            <i class="fas fa-search fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No records found matching the current filters.</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        const startIndex = (currentPage - 1) * recordsPerPage;
        const endIndex = startIndex + recordsPerPage;
        const pageRecords = filteredRecords.slice(startIndex, endIndex);

        let html = '';
        pageRecords.forEach(record => {
            const duration = FaceAttendance.calculateDuration(record.time_in, record.time_out);
            const statusBadge = getStatusBadge(record.status);
            
            html += `
                <tr>
                    <td>
                        <strong>${record.name}</strong>
                        <br>
                        <small class="text-muted">${record.email}</small>
                    </td>
                    <td>${record.email}</td>
                    <td>${formatDisplayDate(record.date)}</td>
                    <td>
                        ${record.time_in ? `<span class="badge bg-success">${record.time_in}</span>` : '-'}
                    </td>
                    <td>
                        ${record.time_out ? `<span class="badge bg-danger">${record.time_out}</span>` : '-'}
                    </td>
                    <td>${duration}</td>
                    <td>${statusBadge}</td>
                </tr>
            `;
        });

        recordsTableBody.innerHTML = html;
    }

    function updatePagination() {
        const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
        const pagination = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let html = '';
        
        // Previous button
        html += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                html += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === currentPage - 3 || i === currentPage + 3) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // Next button
        html += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>
            </li>
        `;
        
        pagination.innerHTML = html;
    }

    function updateStatistics() {
        const today = new Date().toISOString().split('T')[0];
        const todayRecords = allRecords.filter(record => record.date === today);
        
        const presentCount = todayRecords.filter(r => r.status === 'present').length;
        const absentCount = todayRecords.filter(r => r.status === 'absent').length;
        const lateCount = todayRecords.filter(r => r.status === 'late').length;
        
        totalPresent.textContent = presentCount;
        totalAbsent.textContent = absentCount;
        totalLate.textContent = lateCount;
        
        // Get unique users count
        FaceAttendance.makeAPIRequest('/api/users').then(users => {
            totalUsers.textContent = users.length;
        }).catch(() => {
            totalUsers.textContent = '0';
        });
    }

    function getStatusBadge(status) {
        const badges = {
            'present': '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Present</span>',
            'absent': '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Absent</span>',
            'late': '<span class="badge bg-warning text-dark"><i class="fas fa-clock me-1"></i>Late</span>'
        };
        return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
    }

    function formatDisplayDate(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    function showLoading() {
        recordsTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading attendance records...</p>
                </td>
            </tr>
        `;
    }

    function showError(message) {
        recordsTableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="text-danger py-4">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>${message}</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="loadRecords()">
                            <i class="fas fa-retry me-1"></i>Retry
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    function exportRecords() {
        if (filteredRecords.length === 0) {
            FaceAttendance.showAlert('No records to export.', 'warning');
            return;
        }

        const csv = generateCSV(filteredRecords);
        downloadCSV(csv, 'attendance_records.csv');
        FaceAttendance.showAlert('Records exported successfully!', 'success');
    }

    function generateCSV(records) {
        const headers = ['Name', 'Email', 'Date', 'Check In', 'Check Out', 'Duration', 'Status'];
        let csv = headers.join(',') + '\n';
        
        records.forEach(record => {
            const duration = FaceAttendance.calculateDuration(record.time_in, record.time_out);
            const row = [
                `"${record.name}"`,
                `"${record.email}"`,
                record.date,
                record.time_in || '',
                record.time_out || '',
                duration,
                record.status
            ];
            csv += row.join(',') + '\n';
        });
        
        return csv;
    }

    function downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    }

    // Global function for pagination
    window.changePage = function(page) {
        const totalPages = Math.ceil(filteredRecords.length / recordsPerPage);
        if (page >= 1 && page <= totalPages) {
            currentPage = page;
            displayRecords();
            updatePagination();
        }
    };
});
