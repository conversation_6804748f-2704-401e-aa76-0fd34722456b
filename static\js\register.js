// Registration page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const preview = document.getElementById('preview');
    const captureBtn = document.getElementById('captureBtn');
    const submitBtn = document.getElementById('submitBtn');
    const registerForm = document.getElementById('registerForm');
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    
    let capturedImage = null;
    let cameraStarted = false;

    // Capture button click handler
    captureBtn.addEventListener('click', async function() {
        if (!cameraStarted) {
            // Start camera
            const success = await FaceAttendance.startCamera(video);
            if (success) {
                cameraStarted = true;
                captureBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Capture Photo';
                preview.innerHTML = '';
                preview.appendChild(video);
            }
        } else {
            // Capture photo
            capturedImage = FaceAttendance.captureImage(video, canvas);
            
            // Show preview
            const img = document.createElement('img');
            img.src = capturedImage;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '8px';
            
            preview.innerHTML = '';
            preview.appendChild(img);
            
            // Add retake button
            const retakeBtn = document.createElement('button');
            retakeBtn.type = 'button';
            retakeBtn.className = 'btn btn-sm btn-outline-secondary mt-2';
            retakeBtn.innerHTML = '<i class="fas fa-redo me-1"></i>Retake';
            retakeBtn.onclick = retakePhoto;
            preview.appendChild(retakeBtn);
            
            // Stop camera and enable submit
            FaceAttendance.stopCamera();
            video.style.display = 'none';
            cameraStarted = false;
            captureBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Capture Face';
            submitBtn.disabled = false;
        }
    });

    function retakePhoto() {
        capturedImage = null;
        submitBtn.disabled = true;
        preview.innerHTML = `
            <div class="text-muted">
                <i class="fas fa-camera fa-3x mb-2"></i>
                <p>Click "Capture Face" to take a photo</p>
            </div>
        `;
    }

    // Form submission handler
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const name = document.getElementById('name').value.trim();
        const email = document.getElementById('email').value.trim();
        
        if (!name || !email) {
            FaceAttendance.showAlert('Please fill in all required fields.', 'warning');
            return;
        }
        
        if (!capturedImage) {
            FaceAttendance.showAlert('Please capture a face photo first.', 'warning');
            return;
        }
        
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            FaceAttendance.showAlert('Please enter a valid email address.', 'warning');
            return;
        }
        
        try {
            loadingModal.show();
            
            const response = await FaceAttendance.makeAPIRequest('/api/register', 'POST', {
                name: name,
                email: email,
                image: capturedImage
            });
            
            loadingModal.hide();
            
            // Show success message
            FaceAttendance.showAlert(
                `Registration successful! Welcome ${name}. You can now mark attendance using face recognition.`,
                'success'
            );
            
            // Reset form
            registerForm.reset();
            retakePhoto();
            
            // Redirect to attendance page after 3 seconds
            setTimeout(() => {
                window.location.href = '/attendance';
            }, 3000);
            
        } catch (error) {
            loadingModal.hide();
            FaceAttendance.showAlert(error.message || 'Registration failed. Please try again.', 'danger');
        }
    });

    // Form validation
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');
    
    nameInput.addEventListener('input', function() {
        const value = this.value.trim();
        if (value.length < 2) {
            this.setCustomValidity('Name must be at least 2 characters long');
        } else if (!/^[a-zA-Z\s]+$/.test(value)) {
            this.setCustomValidity('Name can only contain letters and spaces');
        } else {
            this.setCustomValidity('');
        }
    });
    
    emailInput.addEventListener('input', function() {
        const value = this.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        FaceAttendance.stopCamera();
    });
});
