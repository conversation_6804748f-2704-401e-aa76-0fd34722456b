#!/usr/bin/env python3
"""
Test script to verify Face Recognition Attendance System installation
"""

import sys
import importlib
import os

def test_python_version():
    """Test if Python version is compatible"""
    print("Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 7:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.7+")
        return False

def test_package_import(package_name, display_name=None):
    """Test if a package can be imported"""
    if display_name is None:
        display_name = package_name
    
    try:
        importlib.import_module(package_name)
        print(f"✓ {display_name} - Installed")
        return True
    except ImportError as e:
        print(f"✗ {display_name} - Not installed ({e})")
        return False

def test_opencv_functionality():
    """Test OpenCV camera functionality"""
    print("Testing OpenCV camera access...")
    try:
        import cv2
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            if ret:
                print("✓ Camera access - Working")
                return True
            else:
                print("✗ Camera access - Cannot read frames")
                return False
        else:
            print("✗ Camera access - Cannot open camera")
            return False
    except Exception as e:
        print(f"✗ Camera access - Error: {e}")
        return False

def test_face_recognition_functionality():
    """Test face_recognition library functionality"""
    print("Testing face recognition functionality...")
    try:
        import face_recognition
        import numpy as np

        # Create a simple test image (black square)
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)

        # Try to find faces (should return empty list)
        face_locations = face_recognition.face_locations(test_image)
        print("✓ Face recognition - Basic functionality working")
        return True
    except Exception as e:
        print(f"✗ Face recognition - Error: {e}")
        return False

def test_mongodb_connection():
    """Test MongoDB connection"""
    print("Testing MongoDB connection...")
    try:
        import pymongo
        from pymongo import MongoClient

        # Try to connect to MongoDB
        client = MongoClient('localhost', 27017, serverSelectionTimeoutMS=3000)

        # Test connection
        client.server_info()

        # Test database access
        db = client.face_attendance_db

        # Try a simple operation
        db.test_collection.insert_one({'test': 'connection'})
        db.test_collection.delete_one({'test': 'connection'})

        client.close()
        print("✓ MongoDB - Connection successful")
        return True

    except Exception as e:
        print(f"✗ MongoDB - Connection failed: {e}")
        print("  Please ensure MongoDB is installed and running")
        return False

def test_file_structure():
    """Test if required files exist"""
    print("Testing file structure...")
    required_files = [
        'app.py',
        'requirements.txt',
        'templates/base.html',
        'templates/index.html',
        'templates/register.html',
        'templates/attendance.html',
        'templates/records.html',
        'static/css/style.css',
        'static/js/main.js',
        'static/js/register.js',
        'static/js/attendance.js',
        'static/js/records.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - Missing")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """Run all tests"""
    print("=" * 50)
    print("Face Recognition Attendance System")
    print("Installation Test")
    print("=" * 50)
    print()
    
    tests_passed = 0
    total_tests = 0
    
    # Test Python version
    total_tests += 1
    if test_python_version():
        tests_passed += 1
    print()
    
    # Test required packages
    packages = [
        ('flask', 'Flask'),
        ('cv2', 'OpenCV'),
        ('face_recognition', 'Face Recognition'),
        ('numpy', 'NumPy'),
        ('PIL', 'Pillow'),
        ('pymongo', 'PyMongo'),
        ('flask_pymongo', 'Flask-PyMongo'),
        ('flask_cors', 'Flask-CORS'),
        ('dotenv', 'python-dotenv'),
        ('werkzeug', 'Werkzeug'),
        ('bson', 'BSON')
    ]
    
    print("Testing package imports...")
    for package, display_name in packages:
        total_tests += 1
        if test_package_import(package, display_name):
            tests_passed += 1
    print()
    
    # Test file structure
    total_tests += 1
    if test_file_structure():
        tests_passed += 1
    print()
    
    # Test OpenCV camera (optional)
    print("Testing hardware functionality...")
    total_tests += 1
    if test_opencv_functionality():
        tests_passed += 1
    print()
    
    # Test face recognition functionality
    total_tests += 1
    if test_face_recognition_functionality():
        tests_passed += 1
    print()

    # Test MongoDB connection
    total_tests += 1
    if test_mongodb_connection():
        tests_passed += 1
    print()

    # Summary
    print("=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Installation is complete.")
        print("\nTo start the application:")
        print("1. Run: python app.py")
        print("2. Open browser to: http://localhost:5000")
    else:
        print("❌ Some tests failed. Please check the installation.")
        print("\nTroubleshooting:")
        print("1. Ensure Python 3.7+ is installed")
        print("2. Run: pip install -r requirements.txt")
        print("3. Check INSTALLATION_GUIDE.md for detailed instructions")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
