#!/usr/bin/env python3
"""
MongoDB setup script for Face Recognition Attendance System
This script initializes the MongoDB database with proper collections and indexes
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime
import json

# MongoDB connection settings
MONGO_HOST = 'localhost'
MONGO_PORT = 27017
DATABASE_NAME = 'face_attendance_db'

def connect_to_mongodb():
    """Connect to MongoDB and return database instance"""
    try:
        client = MongoClient(MONGO_HOST, MONGO_PORT, serverSelectionTimeoutMS=5000)
        # Test connection
        client.server_info()
        db = client[DATABASE_NAME]
        print(f"✓ Connected to MongoDB at {MONGO_HOST}:{MONGO_PORT}")
        print(f"✓ Using database: {DATABASE_NAME}")
        return db, client
    except Exception as e:
        print(f"✗ Failed to connect to MongoDB: {e}")
        print("Please ensure MongoDB is installed and running.")
        return None, None

def create_collections_and_indexes(db):
    """Create collections and indexes for optimal performance"""
    try:
        # Create users collection with indexes
        users_collection = db.users
        
        # Create unique index on email
        users_collection.create_index("email", unique=True)
        print("✓ Created unique index on users.email")
        
        # Create index on created_at for sorting
        users_collection.create_index("created_at")
        print("✓ Created index on users.created_at")
        
        # Create attendance collection with indexes
        attendance_collection = db.attendance
        
        # Create compound index on user_id and date
        attendance_collection.create_index([("user_id", 1), ("date", 1)], unique=True)
        print("✓ Created compound index on attendance.user_id and attendance.date")
        
        # Create index on date for filtering
        attendance_collection.create_index("date")
        print("✓ Created index on attendance.date")
        
        # Create index on user_email for quick lookups
        attendance_collection.create_index("user_email")
        print("✓ Created index on attendance.user_email")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating indexes: {e}")
        return False

def create_sample_data(db):
    """Create sample data for testing (optional)"""
    try:
        users_collection = db.users
        attendance_collection = db.attendance
        
        # Check if sample data already exists
        if users_collection.count_documents({}) > 0:
            print("Sample data already exists, skipping creation.")
            return True
        
        print("Creating sample data...")
        
        # Sample user (without face encoding for demo)
        sample_user = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'face_encoding': [],  # Empty for demo
            'photo_path': 'static/uploads/sample.jpg',
            'created_at': datetime.utcnow()
        }
        
        user_result = users_collection.insert_one(sample_user)
        print(f"✓ Created sample user: {sample_user['name']}")
        
        # Sample attendance record
        sample_attendance = {
            'user_id': user_result.inserted_id,
            'user_name': sample_user['name'],
            'user_email': sample_user['email'],
            'date': datetime.now().date().isoformat(),
            'time_in': datetime.now(),
            'time_out': None,
            'status': 'present'
        }
        
        attendance_collection.insert_one(sample_attendance)
        print(f"✓ Created sample attendance record")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating sample data: {e}")
        return False

def show_database_info(db):
    """Display database information"""
    try:
        print("\n" + "="*50)
        print("DATABASE INFORMATION")
        print("="*50)
        
        # List collections
        collections = db.list_collection_names()
        print(f"Collections: {collections}")
        
        # Show collection stats
        for collection_name in collections:
            collection = db[collection_name]
            count = collection.count_documents({})
            print(f"{collection_name}: {count} documents")
            
            # Show indexes
            indexes = list(collection.list_indexes())
            print(f"  Indexes: {len(indexes)}")
            for index in indexes:
                print(f"    - {index['name']}: {index.get('key', {})}")
        
        print("="*50)
        
    except Exception as e:
        print(f"Error getting database info: {e}")

def main():
    """Main setup function"""
    print("MongoDB Setup for Face Recognition Attendance System")
    print("="*60)
    
    # Connect to MongoDB
    db, client = connect_to_mongodb()
    if not db:
        return False
    
    # Create collections and indexes
    print("\nCreating collections and indexes...")
    if not create_collections_and_indexes(db):
        return False
    
    # Ask user if they want sample data
    create_sample = input("\nCreate sample data for testing? (y/n): ").lower().strip()
    if create_sample == 'y':
        create_sample_data(db)
    
    # Show database information
    show_database_info(db)
    
    # Close connection
    client.close()
    
    print("\n✓ MongoDB setup completed successfully!")
    print("\nNext steps:")
    print("1. Install Python dependencies: pip install -r requirements.txt")
    print("2. Run the application: python app.py")
    print("3. Open browser to: http://localhost:5000")
    
    return True

if __name__ == "__main__":
    main()
