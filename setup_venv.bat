@echo off
echo ========================================
echo Face Recognition Attendance System
echo Virtual Environment Setup
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.7+ from https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
    exit /b 1
)

echo Creating virtual environment...
python -m venv face_attendance_env

echo Activating virtual environment...
call face_attendance_env\Scripts\activate.bat

echo Upgrading pip...
python -m pip install --upgrade pip

echo Installing required packages...
pip install -r requirements.txt

echo.
echo ========================================
echo Virtual Environment Setup Complete!
echo ========================================
echo.
echo To run the application:
echo 1. Run: face_attendance_env\Scripts\activate.bat
echo 2. Run: python app.py
echo 3. Open browser to: http://localhost:5000
echo.
echo To deactivate virtual environment: deactivate
echo.
pause
