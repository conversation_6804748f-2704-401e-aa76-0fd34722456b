#!/usr/bin/env python3
"""
Demo version of Face Recognition Attendance System
This version works without MongoDB and face recognition for testing
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime, date
from werkzeug.utils import secure_filename
import base64

app = Flask(__name__)
app.config['SECRET_KEY'] = 'demo-secret-key'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

CORS(app)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# In-memory storage for demo (replace with MongoDB in production)
users_db = []
attendance_db = []

# Helper functions
def get_next_id(db_list):
    """Get next available ID"""
    if not db_list:
        return 1
    return max(item.get('id', 0) for item in db_list) + 1

def find_user_by_email(email):
    """Find user by email"""
    for user in users_db:
        if user['email'] == email:
            return user
    return None

def find_attendance_by_user_and_date(user_id, date_str):
    """Find attendance record by user and date"""
    for record in attendance_db:
        if record['user_id'] == user_id and record['date'] == date_str:
            return record
    return None

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/register')
def register_page():
    return render_template('register.html')

@app.route('/attendance')
def attendance_page():
    return render_template('attendance.html')

@app.route('/records')
def records_page():
    return render_template('records.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        image_data = data.get('image')
        
        if not all([name, email, image_data]):
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Check if user already exists
        if find_user_by_email(email):
            return jsonify({'error': 'User already registered'}), 400
        
        # Save image (demo version)
        try:
            image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64,
            image_bytes = base64.b64decode(image_data)
            
            filename = secure_filename(f"{email}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
            photo_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            with open(photo_path, 'wb') as f:
                f.write(image_bytes)
        except Exception as e:
            print(f"Image save error: {e}")
            photo_path = None
        
        # Create new user (demo version - no face encoding)
        user_doc = {
            'id': get_next_id(users_db),
            'name': name,
            'email': email,
            'face_encoding': [],  # Empty for demo
            'photo_path': photo_path,
            'created_at': datetime.utcnow().isoformat()
        }
        
        users_db.append(user_doc)
        
        return jsonify({
            'message': 'User registered successfully (Demo Mode)',
            'user_id': user_doc['id']
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/mark_attendance', methods=['POST'])
def mark_attendance():
    try:
        data = request.get_json()
        image_data = data.get('image')
        action = data.get('action', 'in')
        
        if not image_data:
            return jsonify({'error': 'No image provided'}), 400
        
        # Demo mode: simulate face recognition
        # In real app, this would use face_recognition library
        
        # For demo, let's use the first registered user
        if not users_db:
            return jsonify({'error': 'No users registered yet. Please register first.'}), 404
        
        # Use the most recently registered user for demo
        recognized_user = users_db[-1]
        
        # Mark attendance
        today = date.today().isoformat()
        attendance = find_attendance_by_user_and_date(recognized_user['id'], today)
        
        current_time = datetime.now()
        
        if not attendance:
            # Create new attendance record
            attendance = {
                'id': get_next_id(attendance_db),
                'user_id': recognized_user['id'],
                'user_name': recognized_user['name'],
                'user_email': recognized_user['email'],
                'date': today,
                'time_in': None,
                'time_out': None,
                'status': 'present'
            }
            attendance_db.append(attendance)
        
        if action == 'in':
            if attendance.get('time_in'):
                return jsonify({'error': 'Already marked in for today'}), 400
            
            attendance['time_in'] = current_time.isoformat()
            message = f'Welcome {recognized_user["name"]}! Attendance marked for check-in. (Demo Mode)'
            
        else:  # action == 'out'
            if not attendance.get('time_in'):
                return jsonify({'error': 'Must check in first'}), 400
            if attendance.get('time_out'):
                return jsonify({'error': 'Already marked out for today'}), 400
            
            attendance['time_out'] = current_time.isoformat()
            message = f'Goodbye {recognized_user["name"]}! Attendance marked for check-out. (Demo Mode)'
        
        return jsonify({
            'message': message,
            'user': recognized_user['name'],
            'time': current_time.strftime('%H:%M:%S')
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/attendance_records')
def get_attendance_records():
    try:
        result = []
        for record in attendance_db:
            time_in = None
            time_out = None
            
            if record.get('time_in'):
                time_in = datetime.fromisoformat(record['time_in']).strftime('%H:%M:%S')
            if record.get('time_out'):
                time_out = datetime.fromisoformat(record['time_out']).strftime('%H:%M:%S')
            
            result.append({
                'id': record['id'],
                'name': record.get('user_name', 'Unknown'),
                'email': record.get('user_email', 'Unknown'),
                'date': record['date'],
                'time_in': time_in,
                'time_out': time_out,
                'status': record.get('status', 'present')
            })
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users')
def get_users():
    try:
        result = []
        for user in users_db:
            result.append({
                'id': user['id'],
                'name': user['name'],
                'email': user['email'],
                'created_at': user['created_at']
            })
        
        return jsonify(result), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/demo')
def demo_info():
    return jsonify({
        'mode': 'Demo Mode',
        'message': 'This is a demo version without face recognition',
        'users_count': len(users_db),
        'attendance_count': len(attendance_db),
        'features': [
            'User registration (without face encoding)',
            'Attendance marking (simulated recognition)',
            'Records viewing',
            'In-memory storage (data lost on restart)'
        ],
        'limitations': [
            'No actual face recognition',
            'Uses most recent user for attendance',
            'Data not persistent',
            'No MongoDB connection required'
        ]
    })

if __name__ == '__main__':
    print("=" * 60)
    print("Face Recognition Attendance System - DEMO MODE")
    print("=" * 60)
    print("⚠️  This is a demo version with the following limitations:")
    print("   - No actual face recognition (simulated)")
    print("   - Uses in-memory storage (data lost on restart)")
    print("   - Uses most recent registered user for attendance")
    print("   - No MongoDB connection required")
    print()
    print("✅ Demo features available:")
    print("   - User registration with photo capture")
    print("   - Simulated attendance marking")
    print("   - Records viewing and management")
    print("   - Full web interface")
    print()
    print("🚀 Starting demo server...")
    print("📱 Open your browser to: http://localhost:5000")
    print("📊 Demo info available at: http://localhost:5000/demo")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
