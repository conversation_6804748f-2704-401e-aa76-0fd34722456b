// Attendance page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const cameraPreview = document.getElementById('cameraPreview');
    const startCameraBtn = document.getElementById('startCamera');
    const markAttendanceBtn = document.getElementById('markAttendance');
    const timeDisplay = document.getElementById('timeDisplay');
    const dateDisplay = document.getElementById('dateDisplay');
    const statusMessages = document.getElementById('statusMessages');
    const todayAttendance = document.getElementById('todayAttendance');
    
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    const processingModal = new bootstrap.Modal(document.getElementById('processingModal'));
    
    let cameraStarted = false;
    let currentAction = 'in';

    // Initialize time display
    updateTimeDisplay();
    setInterval(updateTimeDisplay, 1000);

    // Load today's attendance
    loadTodayAttendance();

    function updateTimeDisplay() {
        const now = new Date();
        timeDisplay.textContent = FaceAttendance.formatTime(now);
        dateDisplay.textContent = FaceAttendance.formatDate(now);
    }

    // Action radio button handlers
    document.querySelectorAll('input[name="action"]').forEach(radio => {
        radio.addEventListener('change', function() {
            currentAction = this.value;
            updateActionUI();
        });
    });

    function updateActionUI() {
        const checkinLabel = document.querySelector('label[for="checkin"]');
        const checkoutLabel = document.querySelector('label[for="checkout"]');
        
        if (currentAction === 'in') {
            markAttendanceBtn.className = 'btn btn-success w-100';
            markAttendanceBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Check In';
        } else {
            markAttendanceBtn.className = 'btn btn-danger w-100';
            markAttendanceBtn.innerHTML = '<i class="fas fa-sign-out-alt me-2"></i>Check Out';
        }
    }

    // Start camera button handler
    startCameraBtn.addEventListener('click', async function() {
        if (!cameraStarted) {
            const success = await FaceAttendance.startCamera(video);
            if (success) {
                cameraStarted = true;
                cameraPreview.innerHTML = '';
                cameraPreview.appendChild(video);
                
                startCameraBtn.innerHTML = '<i class="fas fa-stop me-2"></i>Stop Camera';
                startCameraBtn.className = 'btn btn-danger w-100';
                markAttendanceBtn.disabled = false;
            }
        } else {
            FaceAttendance.stopCamera();
            cameraStarted = false;
            video.style.display = 'none';
            
            cameraPreview.innerHTML = `
                <div class="text-muted">
                    <i class="fas fa-video fa-3x mb-2"></i>
                    <p>Click "Start Camera" to begin</p>
                </div>
            `;
            
            startCameraBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Start Camera';
            startCameraBtn.className = 'btn btn-primary w-100';
            markAttendanceBtn.disabled = true;
        }
    });

    // Mark attendance button handler
    markAttendanceBtn.addEventListener('click', async function() {
        if (!cameraStarted) {
            FaceAttendance.showAlert('Please start the camera first.', 'warning');
            return;
        }

        try {
            processingModal.show();
            
            // Capture image
            const imageData = FaceAttendance.captureImage(video, canvas);
            
            // Send to server
            const response = await FaceAttendance.makeAPIRequest('/api/mark_attendance', 'POST', {
                image: imageData,
                action: currentAction
            });
            
            processingModal.hide();
            
            // Show success modal
            document.getElementById('successContent').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-user-check fa-3x text-success mb-3"></i>
                    <h4>${response.user}</h4>
                    <p class="lead">${response.message}</p>
                    <p class="text-muted">Time: ${response.time}</p>
                </div>
            `;
            
            successModal.show();
            
            // Refresh today's attendance
            loadTodayAttendance();
            
            // Stop camera after successful attendance
            setTimeout(() => {
                if (cameraStarted) {
                    startCameraBtn.click();
                }
            }, 2000);
            
        } catch (error) {
            processingModal.hide();
            FaceAttendance.showAlert(error.message || 'Failed to mark attendance. Please try again.', 'danger');
        }
    });

    async function loadTodayAttendance() {
        try {
            const records = await FaceAttendance.makeAPIRequest('/api/attendance_records');
            const today = new Date().toISOString().split('T')[0];
            const todayRecords = records.filter(record => record.date === today);
            
            if (todayRecords.length === 0) {
                todayAttendance.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p>No attendance records for today yet.</p>
                    </div>
                `;
                return;
            }
            
            let html = '<div class="table-responsive"><table class="table table-sm">';
            html += '<thead><tr><th>Name</th><th>Check In</th><th>Check Out</th><th>Status</th></tr></thead><tbody>';
            
            todayRecords.forEach(record => {
                const statusClass = getStatusClass(record.status);
                const statusIcon = getStatusIcon(record.status);
                
                html += `
                    <tr>
                        <td><strong>${record.name}</strong></td>
                        <td>${record.time_in || '-'}</td>
                        <td>${record.time_out || '-'}</td>
                        <td>
                            <span class="badge ${statusClass}">
                                <i class="fas fa-${statusIcon} me-1"></i>
                                ${record.status}
                            </span>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
            todayAttendance.innerHTML = html;
            
        } catch (error) {
            console.error('Failed to load today\'s attendance:', error);
            todayAttendance.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <p>Failed to load attendance records.</p>
                </div>
            `;
        }
    }

    function getStatusClass(status) {
        const classes = {
            'present': 'bg-success',
            'absent': 'bg-danger',
            'late': 'bg-warning text-dark'
        };
        return classes[status] || 'bg-secondary';
    }

    function getStatusIcon(status) {
        const icons = {
            'present': 'check',
            'absent': 'times',
            'late': 'clock'
        };
        return icons[status] || 'question';
    }

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        FaceAttendance.stopCamera();
    });
});
