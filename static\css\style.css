/* Custom CSS for Face Recognition Attendance System */

:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Camera and Video Styles */
#video, #canvas {
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#preview, #cameraPreview {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    transition: border-color 0.3s ease;
}

#preview:hover, #cameraPreview:hover {
    border-color: var(--primary-color);
}

/* Status Messages */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0a3622;
}

.alert-danger {
    background-color: #f8d7da;
    color: #58151c;
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

/* Table Styles */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Statistics Cards */
.card.bg-success,
.card.bg-danger,
.card.bg-warning,
.card.bg-info {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card.bg-success:hover,
.card.bg-danger:hover,
.card.bg-warning:hover,
.card.bg-info:hover {
    transform: scale(1.02);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 15px 15px;
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Time Display */
#timeDisplay {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
}

#dateDisplay {
    font-size: 1.1rem;
}

/* Form Controls */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Groups */
.btn-group .btn-check:checked + .btn {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Footer */
footer {
    margin-top: auto;
    background-color: var(--light-color) !important;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    #video, #canvas {
        width: 100%;
        height: auto;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

.status-present {
    background-color: var(--success-color);
}

.status-absent {
    background-color: var(--danger-color);
}

.status-late {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
