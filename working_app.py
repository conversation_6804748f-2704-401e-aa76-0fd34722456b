from flask import Flask, render_template_string, request, jsonify
from datetime import datetime
import json

app = Flask(__name__)

# In-memory storage
users = []
attendance = []

# HTML Templates
HOME_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Face Recognition Attendance System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .feature-card { transition: transform 0.2s; }
        .feature-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🎯 Face Attendance System</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/register">Register</a>
                <a class="nav-link" href="/attendance">Attendance</a>
                <a class="nav-link" href="/records">Records</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">Face Recognition Attendance System</h1>
            <p class="lead">Modern attendance tracking with facial recognition technology</p>
            <div class="alert alert-info">
                <strong>Demo Mode:</strong> Simplified version for testing - No face recognition required
            </div>
        </div>

        <div class="row g-4">
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-user-plus fa-3x text-success"></i>
                        </div>
                        <h5>Register New User</h5>
                        <p>Add new users to the system</p>
                        <a href="/register" class="btn btn-success">Register Now</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-clock fa-3x text-primary"></i>
                        </div>
                        <h5>Mark Attendance</h5>
                        <p>Quick attendance marking</p>
                        <a href="/attendance" class="btn btn-primary">Mark Attendance</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-chart-bar fa-3x text-info"></i>
                        </div>
                        <h5>View Records</h5>
                        <p>Check attendance history</p>
                        <a href="/records" class="btn btn-info">View Records</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-info fa-3x text-warning"></i>
                        </div>
                        <h5>System Status</h5>
                        <p>Users: {{ users_count }} | Records: {{ records_count }}</p>
                        <span class="badge bg-success">Demo Active</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</body>
</html>
"""

REGISTER_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Register User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🎯 Face Attendance System</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link active" href="/register">Register</a>
                <a class="nav-link" href="/attendance">Attendance</a>
                <a class="nav-link" href="/records">Records</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">Register New User</h4>
                    </div>
                    <div class="card-body">
                        <form id="registerForm">
                            <div class="mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" placeholder="Optional">
                            </div>
                            <button type="submit" class="btn btn-success">Register User</button>
                        </form>
                        <div id="message" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const department = document.getElementById('department').value;
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ name, email, department })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('message').innerHTML = 
                        '<div class="alert alert-success">' + result.message + '</div>';
                    document.getElementById('registerForm').reset();
                } else {
                    document.getElementById('message').innerHTML = 
                        '<div class="alert alert-danger">' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('message').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
"""

ATTENDANCE_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Mark Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🎯 Face Attendance System</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/register">Register</a>
                <a class="nav-link active" href="/attendance">Attendance</a>
                <a class="nav-link" href="/records">Records</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Mark Attendance</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Select User (Demo Mode)</label>
                            <select class="form-control" id="userSelect">
                                <option value="">Select a user...</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Action</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="action" id="checkin" value="in" checked>
                                <label class="btn btn-outline-success" for="checkin">Check In</label>
                                <input type="radio" class="btn-check" name="action" id="checkout" value="out">
                                <label class="btn btn-outline-danger" for="checkout">Check Out</label>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary w-100" onclick="markAttendance()">
                            Mark Attendance
                        </button>
                        <div id="message" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load users on page load
        window.onload = function() {
            loadUsers();
        };

        async function loadUsers() {
            try {
                const response = await fetch('/api/users');
                const users = await response.json();
                
                const select = document.getElementById('userSelect');
                select.innerHTML = '<option value="">Select a user...</option>';
                
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = user.name + ' (' + user.email + ')';
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        async function markAttendance() {
            const userId = document.getElementById('userSelect').value;
            const action = document.querySelector('input[name="action"]:checked').value;
            
            if (!userId) {
                document.getElementById('message').innerHTML = 
                    '<div class="alert alert-warning">Please select a user</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/mark_attendance', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ user_id: parseInt(userId), action })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    document.getElementById('message').innerHTML = 
                        '<div class="alert alert-success">' + result.message + '</div>';
                } else {
                    document.getElementById('message').innerHTML = 
                        '<div class="alert alert-danger">' + result.error + '</div>';
                }
            } catch (error) {
                document.getElementById('message').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
"""

RECORDS_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Attendance Records</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">🎯 Face Attendance System</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link" href="/register">Register</a>
                <a class="nav-link" href="/attendance">Attendance</a>
                <a class="nav-link active" href="/records">Records</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">Attendance Records</h4>
            </div>
            <div class="card-body">
                <button class="btn btn-primary mb-3" onclick="loadRecords()">Refresh Records</button>
                <div id="recordsTable"></div>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            loadRecords();
        };

        async function loadRecords() {
            try {
                const response = await fetch('/api/records');
                const records = await response.json();
                
                let html = '<div class="table-responsive"><table class="table table-striped">';
                html += '<thead><tr><th>Name</th><th>Email</th><th>Date</th><th>Check In</th><th>Check Out</th><th>Status</th></tr></thead><tbody>';
                
                if (records.length === 0) {
                    html += '<tr><td colspan="6" class="text-center">No records found</td></tr>';
                } else {
                    records.forEach(record => {
                        html += '<tr>';
                        html += '<td>' + record.name + '</td>';
                        html += '<td>' + record.email + '</td>';
                        html += '<td>' + record.date + '</td>';
                        html += '<td>' + (record.time_in || '-') + '</td>';
                        html += '<td>' + (record.time_out || '-') + '</td>';
                        html += '<td><span class="badge bg-success">Present</span></td>';
                        html += '</tr>';
                    });
                }
                
                html += '</tbody></table></div>';
                document.getElementById('recordsTable').innerHTML = html;
            } catch (error) {
                document.getElementById('recordsTable').innerHTML = 
                    '<div class="alert alert-danger">Error loading records: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
"""

# Routes
@app.route('/')
def home():
    return render_template_string(HOME_TEMPLATE, 
                                users_count=len(users), 
                                records_count=len(attendance))

@app.route('/register')
def register():
    return render_template_string(REGISTER_TEMPLATE)

@app.route('/attendance')
def attendance_page():
    return render_template_string(ATTENDANCE_TEMPLATE)

@app.route('/records')
def records():
    return render_template_string(RECORDS_TEMPLATE)

# API Routes
@app.route('/api/register', methods=['POST'])
def api_register():
    data = request.get_json()
    name = data.get('name')
    email = data.get('email')
    department = data.get('department', '')
    
    if not name or not email:
        return jsonify({'error': 'Name and email are required'}), 400
    
    # Check if user exists
    for user in users:
        if user['email'] == email:
            return jsonify({'error': 'User already exists'}), 400
    
    user = {
        'id': len(users) + 1,
        'name': name,
        'email': email,
        'department': department,
        'created_at': datetime.now().isoformat()
    }
    
    users.append(user)
    return jsonify({'message': f'User {name} registered successfully!'}), 201

@app.route('/api/users')
def api_users():
    return jsonify(users)

@app.route('/api/mark_attendance', methods=['POST'])
def api_mark_attendance():
    data = request.get_json()
    user_id = data.get('user_id')
    action = data.get('action', 'in')
    
    # Find user
    user = None
    for u in users:
        if u['id'] == user_id:
            user = u
            break
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    today = datetime.now().strftime('%Y-%m-%d')
    current_time = datetime.now().strftime('%H:%M:%S')
    
    # Find or create attendance record
    record = None
    for r in attendance:
        if r['user_id'] == user_id and r['date'] == today:
            record = r
            break
    
    if not record:
        record = {
            'id': len(attendance) + 1,
            'user_id': user_id,
            'name': user['name'],
            'email': user['email'],
            'date': today,
            'time_in': None,
            'time_out': None
        }
        attendance.append(record)
    
    if action == 'in':
        if record['time_in']:
            return jsonify({'error': 'Already checked in today'}), 400
        record['time_in'] = current_time
        message = f'Welcome {user["name"]}! Checked in at {current_time}'
    else:
        if not record['time_in']:
            return jsonify({'error': 'Must check in first'}), 400
        if record['time_out']:
            return jsonify({'error': 'Already checked out today'}), 400
        record['time_out'] = current_time
        message = f'Goodbye {user["name"]}! Checked out at {current_time}'
    
    return jsonify({'message': message}), 200

@app.route('/api/records')
def api_records():
    return jsonify(attendance)

if __name__ == '__main__':
    print("🚀 Starting Face Recognition Attendance System (Demo Mode)")
    print("📱 Open your browser to: http://localhost:5000")
    print("=" * 50)
    app.run(debug=True, host='127.0.0.1', port=5000)
