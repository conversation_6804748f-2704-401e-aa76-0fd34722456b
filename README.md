# Face Recognition Attendance System

A modern, secure, and efficient attendance tracking system using facial recognition technology built with Python Flask and OpenCV.

## Features

- **Face Registration**: Register users with their face data and personal information
- **Real-time Face Recognition**: AI-powered face recognition for instant user identification
- **Attendance Tracking**: Automatic check-in/check-out with timestamp recording
- **Comprehensive Records**: View detailed attendance records with filtering and export options
- **Responsive Design**: Modern web interface that works on desktop and mobile devices
- **Secure Data Storage**: SQLite database with encrypted face encodings

## Technology Stack

- **Backend**: Python Flask
- **Face Recognition**: OpenCV, face_recognition library
- **Database**: MongoDB with PyMongo
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Additional Libraries**: NumPy, Pillow, dlib

## Installation

### Prerequisites

- Python 3.7 or higher
- MongoDB Community Server
- Webcam/Camera access
- Modern web browser

### Setup Instructions

1. **Install MongoDB**
   - Download MongoDB Community Server from [mongodb.com](https://www.mongodb.com/try/download/community)
   - Install and start MongoDB service
   - Default connection: `mongodb://localhost:27017`

2. **Clone or download the project**
   ```bash
   cd "Face detection"
   ```

3. **Install required packages**
   ```bash
   pip install -r requirements.txt
   ```

4. **Setup MongoDB database (optional)**
   ```bash
   python setup_mongodb.py
   ```

5. **Run the application**
   ```bash
   python app.py
   ```

6. **Access the application**
   Open your web browser and navigate to: `http://localhost:5000`

## Usage Guide

### 1. Register New Users

1. Navigate to the "Register" page
2. Fill in the user's name and email
3. Click "Capture Face" to take a photo
4. Ensure good lighting and face is clearly visible
5. Click "Register User" to save

### 2. Mark Attendance

1. Go to the "Mark Attendance" page
2. Select "Check In" or "Check Out"
3. Click "Start Camera"
4. Position your face in front of the camera
5. Click "Mark Attendance" for recognition

### 3. View Records

1. Visit the "View Records" page
2. Use filters to search by date, user, or status
3. Export records to CSV format
4. View attendance statistics

## Project Structure

```
Face detection/
├── app.py                 # Main Flask application
├── config.py              # Configuration settings
├── setup_mongodb.py       # MongoDB setup script
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
├── templates/            # HTML templates
│   ├── base.html
│   ├── index.html
│   ├── register.html
│   ├── attendance.html
│   └── records.html
├── static/               # Static files
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   ├── main.js
│   │   ├── register.js
│   │   ├── attendance.js
│   │   └── records.js
│   └── uploads/          # User photos storage
└── MongoDB Database      # face_attendance_db (created automatically)
```

## API Endpoints

- `POST /api/register` - Register new user with face data
- `POST /api/mark_attendance` - Mark attendance using face recognition
- `GET /api/attendance_records` - Retrieve attendance records
- `GET /api/users` - Get list of registered users

## Database Schema (MongoDB Collections)

### Users Collection
- _id (ObjectId, Primary Key)
- name (String)
- email (String, Unique Index)
- face_encoding (Array of Numbers)
- photo_path (String)
- created_at (DateTime)

### Attendance Collection
- _id (ObjectId, Primary Key)
- user_id (ObjectId, Reference to Users)
- user_name (String, Denormalized)
- user_email (String, Denormalized)
- date (String, ISO Date)
- time_in (DateTime)
- time_out (DateTime)
- status (String)

## Security Features

- Face encodings are stored securely in the database
- Input validation and sanitization
- CORS protection
- Secure file upload handling

## Troubleshooting

### Common Issues

1. **Camera not working**
   - Ensure camera permissions are granted
   - Check if camera is being used by another application
   - Try refreshing the browser

2. **Face not recognized**
   - Ensure good lighting conditions
   - Face should be clearly visible and centered
   - Remove glasses if possible during registration

3. **Installation errors**
   - Make sure Python 3.7+ is installed
   - Install Visual Studio Build Tools (Windows)
   - Use virtual environment for clean installation

### System Requirements

- **Minimum**: 4GB RAM, 2GB storage
- **Recommended**: 8GB RAM, 5GB storage
- **Camera**: 720p or higher resolution

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the documentation
3. Create an issue in the repository

## Future Enhancements

- Multi-face detection in single frame
- Advanced reporting and analytics
- Integration with HR systems
- Mobile application
- Cloud deployment options
- Real-time notifications
