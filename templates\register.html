{% extends "base.html" %}

{% block title %}Register User - Face Recognition Attendance System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    Register New User
                </h4>
            </div>
            <div class="card-body">
                <form id="registerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-primary" id="captureBtn">
                                    <i class="fas fa-camera me-2"></i>Capture Face
                                </button>
                                <button type="submit" class="btn btn-success ms-2" id="submitBtn" disabled>
                                    <i class="fas fa-save me-2"></i>Register User
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="text-center">
                                <video id="video" width="300" height="225" autoplay style="display: none;"></video>
                                <canvas id="canvas" width="300" height="225" style="display: none;"></canvas>
                                <div id="preview" class="border rounded p-3" style="min-height: 225px; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                    <div class="text-muted">
                                        <i class="fas fa-camera fa-3x mb-2"></i>
                                        <p>Click "Capture Face" to take a photo</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Registration Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Photo Requirements:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Face clearly visible</li>
                            <li><i class="fas fa-check text-success me-2"></i>Good lighting conditions</li>
                            <li><i class="fas fa-check text-success me-2"></i>Look directly at camera</li>
                            <li><i class="fas fa-check text-success me-2"></i>Remove glasses if possible</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Tips for Best Results:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Use natural lighting</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Keep face centered</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Maintain neutral expression</li>
                            <li><i class="fas fa-lightbulb text-warning me-2"></i>Avoid shadows on face</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Processing Registration...</h5>
                <p class="text-muted">Please wait while we process your face data.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/register.js') }}"></script>
{% endblock %}
