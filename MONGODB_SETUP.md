# MongoDB Setup Guide for Face Recognition Attendance System

## Overview

This guide will help you install and configure MongoDB for the Face Recognition Attendance System. MongoDB is used to store user information, face encodings, and attendance records.

## MongoDB Installation

### Windows

1. **Download MongoDB Community Server**
   - Visit [MongoDB Download Center](https://www.mongodb.com/try/download/community)
   - Select "Windows" platform
   - Choose "msi" package
   - Download the installer

2. **Install MongoDB**
   - Run the downloaded `.msi` file
   - Choose "Complete" installation
   - **Important**: Check "Install MongoDB as a Service"
   - **Important**: Check "Run service as Network Service user"
   - Install MongoDB Compass (optional GUI tool)

3. **Verify Installation**
   - Open Command Prompt as Administrator
   - Run: `mongod --version`
   - Should display MongoDB version information

4. **Start MongoDB Service**
   - MongoDB should start automatically after installation
   - To manually start: `net start MongoDB`
   - To stop: `net stop MongoDB`

### macOS

1. **Using Homebrew (Recommended)**
   ```bash
   # Install Homebrew if not already installed
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   
   # Install MongoDB
   brew tap mongodb/brew
   brew install mongodb-community
   
   # Start MongoDB
   brew services start mongodb/brew/mongodb-community
   ```

2. **Manual Installation**
   - Download from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
   - Extract and follow installation instructions
   - Add MongoDB to PATH

### Linux (Ubuntu/Debian)

1. **Import MongoDB GPG Key**
   ```bash
   wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
   ```

2. **Add MongoDB Repository**
   ```bash
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
   ```

3. **Install MongoDB**
   ```bash
   sudo apt update
   sudo apt install -y mongodb-org
   ```

4. **Start MongoDB**
   ```bash
   sudo systemctl start mongod
   sudo systemctl enable mongod
   ```

## Configuration

### Default Settings
- **Host**: localhost
- **Port**: 27017
- **Database**: face_attendance_db
- **Connection String**: `mongodb://localhost:27017/face_attendance_db`

### Custom Configuration

1. **Edit Configuration File**
   - Windows: `C:\Program Files\MongoDB\Server\6.0\bin\mongod.cfg`
   - macOS/Linux: `/etc/mongod.conf`

2. **Common Configuration Options**
   ```yaml
   # Network interfaces
   net:
     port: 27017
     bindIp: 127.0.0.1
   
   # Storage
   storage:
     dbPath: /var/lib/mongodb
   
   # Logging
   systemLog:
     destination: file
     logAppend: true
     path: /var/log/mongodb/mongod.log
   ```

## Database Setup

### Automatic Setup (Recommended)

1. **Run Setup Script**
   ```bash
   python setup_mongodb.py
   ```

2. **What the script does:**
   - Creates database and collections
   - Sets up indexes for optimal performance
   - Creates sample data (optional)
   - Verifies connection

### Manual Setup

1. **Connect to MongoDB**
   ```bash
   mongo
   ```

2. **Create Database**
   ```javascript
   use face_attendance_db
   ```

3. **Create Collections and Indexes**
   ```javascript
   // Create users collection with unique email index
   db.users.createIndex({ "email": 1 }, { unique: true })
   db.users.createIndex({ "created_at": 1 })
   
   // Create attendance collection with compound index
   db.attendance.createIndex({ "user_id": 1, "date": 1 }, { unique: true })
   db.attendance.createIndex({ "date": 1 })
   db.attendance.createIndex({ "user_email": 1 })
   ```

## Verification

### Test Connection

1. **Using Python Script**
   ```bash
   python test_installation.py
   ```

2. **Using MongoDB Shell**
   ```bash
   mongo --eval "db.adminCommand('ping')"
   ```

3. **Using MongoDB Compass**
   - Open MongoDB Compass
   - Connect to `mongodb://localhost:27017`
   - Should see connection successful

### Check Database

1. **List Databases**
   ```javascript
   show dbs
   ```

2. **Check Collections**
   ```javascript
   use face_attendance_db
   show collections
   ```

3. **Verify Indexes**
   ```javascript
   db.users.getIndexes()
   db.attendance.getIndexes()
   ```

## Troubleshooting

### Common Issues

#### 1. MongoDB Service Not Starting
**Windows:**
```cmd
net start MongoDB
```

**macOS:**
```bash
brew services restart mongodb/brew/mongodb-community
```

**Linux:**
```bash
sudo systemctl restart mongod
sudo systemctl status mongod
```

#### 2. Connection Refused
- Check if MongoDB service is running
- Verify port 27017 is not blocked by firewall
- Check MongoDB logs for errors

#### 3. Permission Denied
**Windows:**
- Run Command Prompt as Administrator
- Check MongoDB service permissions

**macOS/Linux:**
```bash
sudo chown -R mongodb:mongodb /var/lib/mongodb
sudo chown mongodb:mongodb /tmp/mongodb-27017.sock
```

#### 4. Port Already in Use
- Check what's using port 27017: `netstat -an | grep 27017`
- Stop conflicting service or change MongoDB port

### Log Files

**Windows:**
- `C:\Program Files\MongoDB\Server\6.0\log\mongod.log`

**macOS:**
- `/usr/local/var/log/mongodb/mongo.log`

**Linux:**
- `/var/log/mongodb/mongod.log`

## Security Considerations

### Basic Security

1. **Enable Authentication (Production)**
   ```javascript
   use admin
   db.createUser({
     user: "admin",
     pwd: "secure_password",
     roles: ["userAdminAnyDatabase"]
   })
   ```

2. **Update Configuration**
   ```yaml
   security:
     authorization: enabled
   ```

3. **Update Application Configuration**
   ```python
   MONGO_URI = '**************************************************************'
   ```

### Network Security

1. **Bind to Localhost Only**
   ```yaml
   net:
     bindIp: 127.0.0.1
   ```

2. **Use Firewall Rules**
   - Block external access to port 27017
   - Only allow local connections

## Performance Optimization

### Indexes
- Users collection: email (unique), created_at
- Attendance collection: user_id + date (compound), date, user_email

### Memory Settings
```yaml
storage:
  wiredTiger:
    engineConfig:
      cacheSizeGB: 2
```

### Connection Pooling
```python
# In app.py
app.config['MONGO_URI'] = 'mongodb://localhost:27017/face_attendance_db?maxPoolSize=50'
```

## Backup and Restore

### Backup Database
```bash
mongodump --db face_attendance_db --out /path/to/backup
```

### Restore Database
```bash
mongorestore --db face_attendance_db /path/to/backup/face_attendance_db
```

### Automated Backup Script
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --db face_attendance_db --out /backups/mongodb_$DATE
```

## Next Steps

After MongoDB setup:

1. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application**
   ```bash
   python app.py
   ```

3. **Test the system**
   - Register a user
   - Mark attendance
   - View records

4. **Monitor performance**
   - Use MongoDB Compass
   - Check application logs
   - Monitor database size

## Support Resources

- [MongoDB Documentation](https://docs.mongodb.com/)
- [MongoDB Community Forums](https://community.mongodb.com/)
- [MongoDB University](https://university.mongodb.com/)
- [PyMongo Documentation](https://pymongo.readthedocs.io/)
