from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_pymongo import PyMongo
from flask_cors import CORS
import cv2
import face_recognition
import numpy as np
import base64
import os
from datetime import datetime, date
import json
from werkzeug.utils import secure_filename
from bson.objectid import ObjectId
from bson.json_util import dumps
import pymongo

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MONGO_URI'] = 'mongodb://localhost:27017/face_attendance_db'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

mongo = PyMongo(app)
CORS(app)

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# MongoDB Collections
users_collection = mongo.db.users
attendance_collection = mongo.db.attendance

# Create indexes for better performance
try:
    users_collection.create_index("email", unique=True)
    attendance_collection.create_index([("user_id", 1), ("date", 1)])
    print("MongoDB indexes created successfully")
except Exception as e:
    print(f"Index creation info: {e}")

# Helper functions for MongoDB
def serialize_doc(doc):
    """Convert MongoDB document to JSON serializable format"""
    if doc is None:
        return None
    if '_id' in doc:
        doc['_id'] = str(doc['_id'])
    return doc

def serialize_docs(docs):
    """Convert list of MongoDB documents to JSON serializable format"""
    return [serialize_doc(doc) for doc in docs]

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/register')
def register_page():
    return render_template('register.html')

@app.route('/attendance')
def attendance_page():
    return render_template('attendance.html')

@app.route('/records')
def records_page():
    return render_template('records.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        name = data.get('name')
        email = data.get('email')
        image_data = data.get('image')

        if not all([name, email, image_data]):
            return jsonify({'error': 'Missing required fields'}), 400

        # Check if user already exists
        existing_user = users_collection.find_one({'email': email})
        if existing_user:
            return jsonify({'error': 'User already registered'}), 400

        # Decode base64 image
        image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64,
        image_bytes = base64.b64decode(image_data)

        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Find face encodings
        face_locations = face_recognition.face_locations(rgb_image)
        if not face_locations:
            return jsonify({'error': 'No face detected in the image'}), 400

        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        if not face_encodings:
            return jsonify({'error': 'Could not encode face'}), 400

        # Save image
        filename = secure_filename(f"{email}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg")
        photo_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        cv2.imwrite(photo_path, image)

        # Create new user document
        user_doc = {
            'name': name,
            'email': email,
            'face_encoding': face_encodings[0].tolist(),  # Store as list directly
            'photo_path': photo_path,
            'created_at': datetime.utcnow()
        }

        # Insert user into MongoDB
        result = users_collection.insert_one(user_doc)

        return jsonify({
            'message': 'User registered successfully',
            'user_id': str(result.inserted_id)
        }), 201

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/mark_attendance', methods=['POST'])
def mark_attendance():
    try:
        data = request.get_json()
        image_data = data.get('image')
        action = data.get('action', 'in')  # 'in' or 'out'

        if not image_data:
            return jsonify({'error': 'No image provided'}), 400

        # Decode base64 image
        image_data = image_data.split(',')[1]
        image_bytes = base64.b64decode(image_data)

        # Convert to numpy array
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Find face encodings
        face_locations = face_recognition.face_locations(rgb_image)
        if not face_locations:
            return jsonify({'error': 'No face detected'}), 400

        face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
        if not face_encodings:
            return jsonify({'error': 'Could not encode face'}), 400

        # Compare with registered users
        users = users_collection.find({})
        recognized_user = None

        for user in users:
            stored_encoding = np.array(user['face_encoding'])
            matches = face_recognition.compare_faces([stored_encoding], face_encodings[0])

            if matches[0]:
                recognized_user = user
                break

        if not recognized_user:
            return jsonify({'error': 'Face not recognized'}), 404

        # Mark attendance
        today = date.today().isoformat()
        attendance = attendance_collection.find_one({
            'user_id': recognized_user['_id'],
            'date': today
        })

        current_time = datetime.now()

        if not attendance:
            # Create new attendance record
            attendance_doc = {
                'user_id': recognized_user['_id'],
                'user_name': recognized_user['name'],
                'user_email': recognized_user['email'],
                'date': today,
                'time_in': None,
                'time_out': None,
                'status': 'present'
            }
            attendance_collection.insert_one(attendance_doc)
            attendance = attendance_doc

        if action == 'in':
            if attendance.get('time_in'):
                return jsonify({'error': 'Already marked in for today'}), 400

            attendance_collection.update_one(
                {'_id': attendance.get('_id', ObjectId())},
                {'$set': {'time_in': current_time}}
            )
            message = f'Welcome {recognized_user["name"]}! Attendance marked for check-in.'

        else:  # action == 'out'
            if not attendance.get('time_in'):
                return jsonify({'error': 'Must check in first'}), 400
            if attendance.get('time_out'):
                return jsonify({'error': 'Already marked out for today'}), 400

            attendance_collection.update_one(
                {'_id': attendance.get('_id', ObjectId())},
                {'$set': {'time_out': current_time}}
            )
            message = f'Goodbye {recognized_user["name"]}! Attendance marked for check-out.'

        return jsonify({
            'message': message,
            'user': recognized_user['name'],
            'time': current_time.strftime('%H:%M:%S')
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/attendance_records')
def get_attendance_records():
    try:
        # Get all attendance records with user information
        records = attendance_collection.find({}).sort('date', -1)

        result = []
        for record in records:
            result.append({
                'id': str(record['_id']),
                'name': record.get('user_name', 'Unknown'),
                'email': record.get('user_email', 'Unknown'),
                'date': record['date'],
                'time_in': record['time_in'].strftime('%H:%M:%S') if record.get('time_in') else None,
                'time_out': record['time_out'].strftime('%H:%M:%S') if record.get('time_out') else None,
                'status': record.get('status', 'present')
            })

        return jsonify(result), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users')
def get_users():
    try:
        users = users_collection.find({}, {'face_encoding': 0})  # Exclude face_encoding for performance
        result = []
        for user in users:
            result.append({
                'id': str(user['_id']),
                'name': user['name'],
                'email': user['email'],
                'created_at': user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            })

        return jsonify(result), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Test MongoDB connection
def test_mongodb_connection():
    try:
        # Test connection
        mongo.db.command('ping')
        print("✓ MongoDB connection successful")
        return True
    except Exception as e:
        print(f"✗ MongoDB connection failed: {e}")
        print("Please ensure MongoDB is running on localhost:27017")
        return False

if __name__ == '__main__':
    print("Starting Face Recognition Attendance System with MongoDB...")

    # Test MongoDB connection before starting the app
    if test_mongodb_connection():
        print("Starting Flask application...")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("Failed to start application due to MongoDB connection issues.")
        print("Please install and start MongoDB, then try again.")
