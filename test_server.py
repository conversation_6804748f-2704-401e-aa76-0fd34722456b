#!/usr/bin/env python3
"""
Simple test server to verify <PERSON><PERSON><PERSON> is working
"""

from flask import <PERSON>lask, render_template, jsonify
import os

app = Flask(__name__)

@app.route('/')
def index():
    try:
        return render_template('index.html')
    except Exception as e:
        return f"Template error: {str(e)}"

@app.route('/test')
def test():
    return jsonify({
        'status': 'working',
        'message': 'Flask server is running correctly',
        'templates_exist': os.path.exists('templates'),
        'static_exist': os.path.exists('static'),
        'template_files': os.listdir('templates') if os.path.exists('templates') else []
    })

@app.route('/simple')
def simple():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Page</title>
    </head>
    <body>
        <h1>Flask Server is Working!</h1>
        <p>This is a simple test page.</p>
        <a href="/test">Test JSON endpoint</a>
    </body>
    </html>
    """

if __name__ == '__main__':
    print("Starting simple test server...")
    print("Open browser to: http://localhost:5000/simple")
    app.run(debug=True, host='0.0.0.0', port=5000)
